#!/usr/bin/env python3
"""
GitLab Webhook Secret Adder

This script adds a secret token to specific webhook URLs in GitLab projects.
It targets webhooks with URLs matching the pattern:
https://mrnotifybot.inspectorio.com/webhook?channel_id=GJSA7EA1W&comment_cc_id=U03G4PG1BHV
(or similar URLs without the pipeline_channel_id parameter)

Usage:
    python add_webhook_secret.py --token TOKEN [options]

Options:
    --token TOKEN            GitLab personal access token (required)
    --url GITLAB_URL         GitLab instance URL (default: https://gitlab.com)
    --project PROJECT_NAME   Optional: Name of the specific project to update
                             If not provided, all accessible projects will be checked
    --secret SECRET          Secret token to add to the webhooks (default: Uh2oeRaesheisiew)
    --dry-run                Run without making actual changes
    --verbose                Enable verbose output

Example:
    python add_webhook_secret.py --token glpat-XXXXXXXXXXXX --project my-project
"""

import argparse
import re
import sys
import urllib.parse
from typing import List, Optional, Dict, Any

try:
    import gitlab
except ImportError:
    print("Required package 'python-gitlab' is not installed.")
    print("Please install it using: pip install python-gitlab")
    sys.exit(1)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Add secret token to GitLab webhooks")
    parser.add_argument("--project", help="Name of the specific project to update")
    parser.add_argument("--token", required=True, help="GitLab personal access token")
    parser.add_argument("--url", default="https://gitlab.com", help="GitLab instance URL")
    parser.add_argument("--secret", default="Uh2oeRaesheisiew", help="Secret token to add to webhooks")
    parser.add_argument("--dry-run", action="store_true", help="Run without making actual changes")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    return parser.parse_args()


def get_projects(gl, project_name: Optional[str] = None) -> List[Any]:
    """
    Get GitLab projects based on the provided name or all accessible projects.
    
    Args:
        gl: GitLab client instance
        project_name: Optional project name to filter by
        
    Returns:
        List of GitLab project objects
    """
    if project_name:
        # Search for projects matching the name
        projects = gl.projects.list(search=project_name)
        if not projects:
            print(f"No projects found matching '{project_name}'")
            return []
        
        # Filter for exact match if possible
        exact_matches = [p for p in projects if p.name == project_name or p.path == project_name]
        if exact_matches:
            return exact_matches
        return projects
    else:
        # Get all projects the user has access to
        return gl.projects.list(all=True)


def is_target_webhook(webhook_url: str) -> bool:
    """
    Check if a webhook URL matches our target pattern.
    
    Args:
        webhook_url: The webhook URL to check
        
    Returns:
        True if the webhook URL matches our target pattern, False otherwise
    """
    # Check if this is our target webhook base URL
    if not webhook_url.startswith("https://mrnotifybot.inspectorio.com/webhook"):
        return False
    
    # Parse the URL
    parsed_url = urllib.parse.urlparse(webhook_url)
    query_params = urllib.parse.parse_qs(parsed_url.query)
    
    # Check if this is a target webhook (has channel_id=GJSA7EA1W and comment_cc_id=U03G4PG1BHV)
    # and does NOT have pipeline_channel_id
    return (
        'channel_id' in query_params and 
        'GJSA7EA1W' in query_params['channel_id'] and
        'comment_cc_id' in query_params and 
        'U03G4PG1BHV' in query_params['comment_cc_id'] and
        'pipeline_channel_id' not in query_params
    )


def update_project_webhooks(project, secret: str, dry_run: bool = False, verbose: bool = False) -> int:
    """
    Update webhooks in a project by adding a secret token.
    
    Args:
        project: GitLab project object
        secret: Secret token to add to webhooks
        dry_run: If True, don't make actual changes
        verbose: If True, print detailed information
        
    Returns:
        Number of webhooks updated
    """
    if verbose:
        print(f"Checking project: {project.name} (ID: {project.id})")
    
    # Get all webhooks for the project
    hooks = project.hooks.list(all=True)
    
    if verbose:
        print(f"  Found {len(hooks)} webhooks")
    
    updated_count = 0
    
    for hook in hooks:
        # Check if the webhook URL matches our target pattern
        if is_target_webhook(hook.url):
            old_url = hook.url
            old_secret = hook.token if hasattr(hook, 'token') else None
            
            print(f"Project: {project.name}")
            print(f"  Webhook ID: {hook.id}")
            print(f"  URL: {hook.url}")
            print(f"  Current Secret: {old_secret if old_secret else 'None'}")
            print(f"  New Secret: {secret}")
            
            if not dry_run:
                # Update the webhook with the secret token
                hook.token = secret
                hook.save()
                print("  ✅ Secret added successfully")
            else:
                print("  ⚠️ Dry run - no changes made")
            
            updated_count += 1
        elif verbose:
            print(f"  Webhook ID {hook.id} does not match target pattern")
    
    return updated_count


def main():
    """Main function to add secret tokens to GitLab webhooks."""
    args = parse_arguments()
    
    # Initialize GitLab client
    try:
        gl = gitlab.Gitlab(args.url, private_token=args.token)
        gl.auth()
        print(f"Authenticated as {gl.user.username}")
    except Exception as e:
        print(f"Error authenticating with GitLab: {e}")
        sys.exit(1)
    
    # Get projects
    projects = get_projects(gl, args.project)
    
    if not projects:
        print("No projects to process.")
        sys.exit(0)
    
    print(f"Found {len(projects)} project(s) to check")
    
    # Process each project
    total_updated = 0
    
    for project in projects:
        updated = update_project_webhooks(
            project, 
            args.secret, 
            dry_run=args.dry_run,
            verbose=args.verbose
        )
        total_updated += updated
    
    # Summary
    if args.dry_run:
        print(f"\nDry run completed. Secret would be added to {total_updated} webhook(s).")
    else:
        print(f"\nUpdate completed. Secret added to {total_updated} webhook(s).")


if __name__ == "__main__":
    main()
